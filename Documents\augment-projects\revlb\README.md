# Discord Contribution Tracking Bot

A Discord bot that tracks member contributions for <PERSON><PERSON>, <PERSON><PERSON><PERSON> of Shards, and EXP.

## Features

- **User Sync**: Link Discord accounts to in-game names
- **Contribution Tracking**: Track IGM, Shulker of Shards, and EXP contributions
- **Auto Summary**: Automatically updates a summary embed in a specified channel
- **Data Persistence**: Uses SQLite database to store all data

## Commands

### User Commands

#### `/sync <in_game_name>`
Links your Discord account to your in-game name. **This must be done before adding any contributions.**

Example: `/sync PlayerName123`

#### `/requirements <type> <amount>`
Adds a contribution to your account. Available types:
- **IGM**: In-game money
- **SHULKER OF SHARDS**: Number of shulkers
- **EXP**: Experience points

**Supported Number Formats:**
- `1b`, `1.5b` (billions)
- `200m`, `500m` (millions)
- `1,000,000,000` (raw numbers with commas)
- `********` (raw numbers)
- `500k` (thousands)
- `5` (plain numbers for shulkers)

Examples:
- `/requirements IGM 1.2B`
- `/requirements SHULKEROFSHARDS 5`
- `/requirements EXP 200M`

### Admin Commands (Require Administrator Permissions)

#### `/requirementsremove <in_game_name> [type] [amount]`
Remove contributions for a specific user.
- If `amount` is specified: removes only that specific amount entry
- If `type` is specified but no amount: removes all contributions of that type
- If `type` is "ALL" or not specified: removes all contributions

Examples:
- `/requirementsremove PlayerName123 IGM 1b` (remove specific 1b IGM entry)
- `/requirementsremove PlayerName123 IGM` (remove all IGM contributions)
- `/requirementsremove PlayerName123 ALL` (remove all contributions)

#### `/deleteuser <in_game_name>`
Completely delete a user and all their contribution data.

Example: `/deleteuser PlayerName123`

#### `/listusers`
List all registered users with their Discord mentions. Shows up to 20 users per field.

#### `/monthwipe`
**🧹 MONTH RESET COMMAND** - Wipe all contributions but keep user accounts.
- Deletes all contribution records and historical data
- **Preserves all user accounts and sync data**
- Users remain synced and can immediately add new contributions
- Perfect for monthly resets and fresh starts
- Requires confirmation with a 30-second timeout for safety

#### `/pointvalue`
**⚙️ ADMIN COMMAND** - Configure point values for contribution types.
- Set custom point values for EXP, Shulkers, and IGM
- Examples: `0.000001` for 1 point per 1M EXP, `1.0` for 1 point per shulker
- Updates leaderboard rankings immediately
- Shows current point values after changes

#### `/resetleaderboard`
**⚠️ DANGER COMMAND** - Completely reset the entire leaderboard by deleting ALL data.
- Deletes all user accounts and sync data
- Deletes all contribution records
- Deletes all historical data
- **This action CANNOT be undone!**
- Requires confirmation with a 30-second timeout for safety

## Setup

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Configure environment variables in `.env`:
   ```
   DISCORD_TOKEN=your_bot_token_here
   CHANNEL_ID=your_channel_id_here
   ```

3. Run the bot:
   ```bash
   python bot.py
   ```

## Database

The bot uses SQLite database (`contributions.db`) with two tables:
- `users`: Links Discord IDs to in-game names
- `contributions`: Stores all contribution records

## Enhanced Contribution Leaderboard

The bot automatically maintains a beautiful, professional leaderboard embed that features:

### 🎨 **Visual Design**
- **🏆 Leaderboard Style**: Ranked display with medals (🥇🥈🥉) for top contributors
- **📊 Progress Bars**: Visual progress bars showing relative contribution levels
- **🌈 Dynamic Colors**: Different color themes for each page
- **📋 Code Blocks**: Clean, formatted display with monospace fonts
- **⏰ Timestamps**: Shows last update time

### 📄 **Pagination & Navigation**
- **9 users per page** in a 3x3 grid layout
- **Enhanced Controls**: First/Previous/Next/Last navigation buttons
- **📄 Page Counter**: Shows current page and total pages
- **🔄 Refresh Button**: Manual refresh without waiting for auto-updates
- **⏱️ 10-minute timeout** for interactive buttons

### 📈 **Smart Features**
- **🏅 Ranking System**: Automatic ranking with special emojis for top 10
- **⚖️ Weighted Sorting**: IGM and EXP weighted higher than shulkers for ranking
- **📊 Grand Totals**: Shows total contributions across all members
- **🔢 Smart Formatting**: Numbers displayed as 1B, 200M, 500K with commas
- **📱 Responsive Layout**: Automatically balances fields for clean appearance

### 🔄 **Real-time Updates**
- Updates automatically when contributions are added/removed
- Maintains pagination state during updates
- Shows contributor count and grand totals

## Files

- `bot.py`: Main bot application
- `database.py`: Database operations and queries
- `requirements.txt`: Python dependencies
- `.env`: Environment variables (not tracked in git)
- `contributions.db`: SQLite database (created automatically)
