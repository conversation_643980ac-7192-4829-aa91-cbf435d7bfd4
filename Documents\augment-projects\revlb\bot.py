import discord
from discord.ext import commands
from discord import app_commands
import os
from dotenv import load_dotenv
import database
import asyncio
from datetime import datetime, timedelta
import calendar

# Load environment variables
load_dotenv()

# Bot setup
intents = discord.Intents.default()
intents.message_content = True
bot = commands.Bot(command_prefix='!', intents=intents)

CHANNEL_ID = int(os.getenv('CHANNEL_ID'))

# Admin user IDs
ADMIN_IDS = [572991971359195138, 262718449065787394]  # Cerv's Discord ID, Additional admin

# Allowed channel for commands
ALLOWED_CHANNEL_ID = 1287933885933555813

def is_admin(user_id):
    """Check if user has admin permissions."""
    return user_id in ADMIN_IDS

def check_allowed_channel(interaction):
    """Check if command is being used in the allowed channel."""
    if interaction.channel_id != ALLOWED_CHANNEL_ID:
        return False
    return True

async def send_wrong_channel_message(interaction):
    """Send error message for wrong channel usage."""
    embed = discord.Embed(
        title="❌ Wrong Channel",
        description=f"This command can only be used in <#{ALLOWED_CHANNEL_ID}>",
        color=discord.Color.red()
    )
    if interaction.response.is_done():
        await interaction.followup.send(embed=embed, ephemeral=True)
    else:
        await interaction.response.send_message(embed=embed, ephemeral=True)

def get_week_range(week_num):
    """Get the date range for a specific week of the current month."""
    now = datetime.now()
    year = now.year
    month = now.month

    # Get first day of the month
    first_day = datetime(year, month, 1)

    # Find the first Monday of the month (or the 1st if it's a Monday)
    days_to_monday = (7 - first_day.weekday()) % 7
    if first_day.weekday() == 0:  # If 1st is Monday
        days_to_monday = 0
    first_monday = first_day + timedelta(days=days_to_monday)

    # Calculate week start and end
    week_start = first_monday + timedelta(weeks=week_num-1)
    week_end = week_start + timedelta(days=6, hours=23, minutes=59, seconds=59)

    # Make sure we don't go beyond the current month
    last_day = datetime(year, month, calendar.monthrange(year, month)[1], 23, 59, 59)
    if week_end > last_day:
        week_end = last_day

    return week_start, week_end

def get_month_range():
    """Get the date range for the current month."""
    now = datetime.now()
    year = now.year
    month = now.month

    first_day = datetime(year, month, 1)
    last_day = datetime(year, month, calendar.monthrange(year, month)[1], 23, 59, 59)

    return first_day, last_day

def filter_contributions_by_date(contributions, start_date, end_date):
    """Filter contributions by date range."""
    filtered = []
    for ign, contrib_type, amount, timestamp in contributions:
        # Parse timestamp
        try:
            contrib_date = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            if start_date <= contrib_date <= end_date:
                filtered.append((ign, contrib_type, amount))
        except:
            # If timestamp parsing fails, include the contribution
            filtered.append((ign, contrib_type, amount))

    return filtered

def parse_amount(amount_str):
    """Parse various number formats into standardized values."""
    # Remove whitespace and convert to lowercase
    amount_str = amount_str.strip().lower()

    # Remove commas
    amount_str = amount_str.replace(',', '')

    # Handle different suffixes - convert everything to a common base unit
    # Check for trillion variants
    if (amount_str.endswith('trillion') or amount_str.endswith('tril') or
        amount_str.endswith('t')):
        if amount_str.endswith('trillion'):
            amount_str = amount_str[:-8]  # Remove 'trillion'
        elif amount_str.endswith('tril'):
            amount_str = amount_str[:-4]  # Remove 'tril'
        else:  # ends with 't'
            amount_str = amount_str[:-1]  # Remove 't'
        base_value = float(amount_str)
        return base_value * 1000000000000  # Convert to base units

    # Check for billion variants
    elif (amount_str.endswith('billion') or amount_str.endswith('bil') or
          amount_str.endswith('b')):
        if amount_str.endswith('billion'):
            amount_str = amount_str[:-7]  # Remove 'billion'
        elif amount_str.endswith('bil'):
            amount_str = amount_str[:-3]  # Remove 'bil'
        else:  # ends with 'b'
            amount_str = amount_str[:-1]  # Remove 'b'
        base_value = float(amount_str)
        return base_value * 1000000000  # Convert to base units

    # Check for million variants
    elif (amount_str.endswith('million') or amount_str.endswith('mil') or
          amount_str.endswith('m')):
        if amount_str.endswith('million'):
            amount_str = amount_str[:-7]  # Remove 'million'
        elif amount_str.endswith('mil'):
            amount_str = amount_str[:-3]  # Remove 'mil'
        else:  # ends with 'm'
            amount_str = amount_str[:-1]  # Remove 'm'
        base_value = float(amount_str)
        return base_value * 1000000  # Convert to base units

    # Check for thousand variants
    elif (amount_str.endswith('thousand') or amount_str.endswith('thou') or
          amount_str.endswith('k')):
        if amount_str.endswith('thousand'):
            amount_str = amount_str[:-8]  # Remove 'thousand'
        elif amount_str.endswith('thou'):
            amount_str = amount_str[:-4]  # Remove 'thou'
        else:  # ends with 'k'
            amount_str = amount_str[:-1]  # Remove 'k'
        base_value = float(amount_str)
        return base_value * 1000  # Convert to base units

    else:
        # No suffix - treat as base units
        try:
            base_value = float(amount_str)
            return base_value  # Keep as base units
        except ValueError:
            raise ValueError(f"Cannot parse amount: {amount_str}")

def format_number(value):
    """Format a number for display with appropriate units."""
    if value >= 1000000000000:
        # Show as trillions
        trillions = value / 1000000000000
        if trillions == int(trillions):
            return f"{int(trillions)}T"
        else:
            return f"{trillions:.1f}T"
    elif value >= 1000000000:
        # Show as billions
        billions = value / 1000000000
        if billions == int(billions):
            return f"{int(billions)}B"
        else:
            return f"{billions:.1f}B"
    elif value >= 1000000:
        # Show as millions
        millions = value / 1000000
        if millions == int(millions):
            return f"{int(millions)}M"
        else:
            return f"{millions:.1f}M"
    elif value >= 1000:
        # Show as thousands
        thousands = value / 1000
        if thousands == int(thousands):
            return f"{int(thousands)}K"
        else:
            return f"{thousands:.1f}K"
    elif value > 0:
        # Show as base units
        if value == int(value):
            return f"{int(value)}"
        else:
            return f"{value:.1f}"
    else:
        return "0"

async def calculate_user_points(contribs):
    """Calculate total points for a user based on database point values."""
    point_values = await database.get_point_values()
    total_points = 0
    for contrib_type, amount in contribs.items():
        if contrib_type in point_values:
            total_points += amount * point_values[contrib_type]
    return total_points

@bot.event
async def on_ready():
    """Event triggered when bot is ready."""
    print(f'{bot.user} has logged in!')
    print(f'Bot is in {len(bot.guilds)} guilds')
    await database.init_database()
    print("Database initialized")

    # Sync slash commands
    try:
        print("Starting command sync...")
        synced = await bot.tree.sync()
        print(f"Synced {len(synced)} command(s)")
        for cmd in synced:
            print(f"  - {cmd.name}")
    except Exception as e:
        print(f"Failed to sync commands: {e}")
        import traceback
        traceback.print_exc()

    # Delete the old unwanted message
    try:
        print("Cleaning up old messages...")
        channel = bot.get_channel(CHANNEL_ID)
        if channel:
            old_message = await channel.fetch_message(1377547214657945652)
            await old_message.delete()
            print("Deleted old leaderboard message")
    except discord.NotFound:
        print("Old message already deleted or not found")
    except Exception as e:
        print(f"Could not delete old message: {e}")

    # Load/update the leaderboard on startup
    try:
        print("Loading leaderboard...")
        await update_summary_embed()
        print("Leaderboard loaded successfully!")
    except Exception as e:
        print(f"Failed to load leaderboard: {e}")
        import traceback
        traceback.print_exc()

@bot.tree.command(name="sync", description="Sync your Discord account with your in-game name")
@app_commands.describe(in_game_name="Your in-game name")
async def sync_command(interaction: discord.Interaction, in_game_name: str):
    """Sync Discord user with in-game name."""
    # Check if command is used in allowed channel
    if not check_allowed_channel(interaction):
        await send_wrong_channel_message(interaction)
        return
    success = await database.sync_user(interaction.user.id, in_game_name)

    if success:
        embed = discord.Embed(
            title="✅ Sync Successful",
            description=f"Your Discord account has been synced with in-game name: **{in_game_name}**",
            color=discord.Color.green()
        )
    else:
        embed = discord.Embed(
            title="❌ Sync Failed",
            description="There was an error syncing your account. Please try again.",
            color=discord.Color.red()
        )

    await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="requirements", description="Add your contributions")
@app_commands.describe(
    contribution_type="Type of contribution",
    amount="Amount contributed"
)
@app_commands.choices(contribution_type=[
    app_commands.Choice(name="IGM", value="IGM"),
    app_commands.Choice(name="SHULKER OF SHARDS", value="SHULKEROFSHARDS"),
    app_commands.Choice(name="EXP", value="EXP")
])
async def requirements_command(interaction: discord.Interaction, contribution_type: str, amount: str):
    """Add a contribution."""
    try:
        # Check if command is used in allowed channel
        if not check_allowed_channel(interaction):
            await send_wrong_channel_message(interaction)
            return

        # Acknowledge the interaction immediately to prevent timeouts
        await interaction.response.defer()

        # Quick sync check only
        if not await database.is_user_synced(interaction.user.id):
            embed = discord.Embed(
                title="❌ Not Synced",
                description="You need to run `/sync <in_game_name>` first before adding contributions!",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Process everything in background to avoid timeout
        bot.loop.create_task(process_contribution_background(interaction.user.id, contribution_type, amount, interaction))

    except discord.errors.NotFound:
        # Interaction expired
        print(f"Interaction expired for {interaction.user.display_name}")
        # Still try to process the contribution in background
        try:
            bot.loop.create_task(process_contribution_background(interaction.user.id, contribution_type, amount, interaction))
        except:
            pass
    except Exception as e:
        print(f"Error in requirements command: {e}")
        try:
            if not interaction.response.is_done():
                embed = discord.Embed(
                    title="❌ Error",
                    description="An error occurred while processing your contribution. Please try again.",
                    color=discord.Color.red()
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
        except:
            pass

async def process_contribution_background(user_id: int, contribution_type: str, amount: str, interaction: discord.Interaction):
    """Process contribution in background and send response."""
    try:
        # Get user's IGN and current contributions
        ign = await database.get_user_ign(user_id)
        current_contributions = await database.get_user_contributions(user_id)
        old_total = current_contributions.get(contribution_type, 0)

        # Add contribution
        await database.add_contribution(user_id, contribution_type, amount)

        # Calculate new total efficiently
        numeric_amount = parse_amount(amount)
        new_total = old_total + numeric_amount

        # Format the totals for display
        old_total_display = format_number(old_total) if old_total > 0 else "0"
        new_total_display = format_number(new_total)

        embed = discord.Embed(
            title="✅ Contribution Added",
            description=f"**{ign}** contributed **{amount}** {contribution_type} ({old_total_display} → {new_total_display})",
            color=discord.Color.green()
        )

        await interaction.followup.send(embed=embed)

        # Update the summary embed
        await update_summary_embed()

    except Exception as e:
        print(f"Error processing contribution in background: {e}")
        try:
            embed = discord.Embed(
                title="❌ Error",
                description="An error occurred while processing your contribution. Please try again.",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
        except:
            pass

@bot.tree.command(name="requirementsremove", description="[ADMIN] Remove contributions for a user")
@app_commands.describe(
    in_game_name="The in-game name of the user",
    contribution_type="Type of contribution to remove (leave empty to remove all)",
    amount="Specific amount to remove (leave empty to remove all of that type)"
)
@app_commands.choices(contribution_type=[
    app_commands.Choice(name="IGM", value="IGM"),
    app_commands.Choice(name="SHULKER OF SHARDS", value="SHULKEROFSHARDS"),
    app_commands.Choice(name="EXP", value="EXP"),
    app_commands.Choice(name="ALL", value="ALL")
])
async def requirements_remove_command(interaction: discord.Interaction, in_game_name: str, contribution_type: str = None, amount: str = None):
    """Remove contributions for a user."""
    try:
        # Check if command is used in allowed channel
        if not check_allowed_channel(interaction):
            await send_wrong_channel_message(interaction)
            return

        # Check if user has admin permissions
        if not (interaction.user.guild_permissions.administrator or is_admin(interaction.user.id)):
            embed = discord.Embed(
                title="❌ Access Denied",
                description="You need administrator permissions to use this command.",
                color=discord.Color.red()
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        # Check if user exists
        user_info = await database.get_user_by_ign(in_game_name)
        if not user_info:
            embed = discord.Embed(
                title="❌ User Not Found",
                description=f"No user found with in-game name: **{in_game_name}**",
                color=discord.Color.red()
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        # Defer the response for longer operations
        await interaction.response.defer()

        # Remove contributions
        contrib_type = None if contribution_type == "ALL" else contribution_type
        changes = await database.remove_user_contributions(in_game_name, contrib_type, amount)

        if changes > 0:
            if amount and contribution_type and contribution_type != "ALL":
                type_text = f"{amount} {contribution_type}"
            elif contribution_type and contribution_type != "ALL":
                type_text = f"all {contribution_type} contributions"
            else:
                type_text = "all contributions"

            embed = discord.Embed(
                title="✅ Contributions Removed",
                description=f"Removed {type_text} for **{in_game_name}** ({changes} records deleted)",
                color=discord.Color.green()
            )
        else:
            if amount and contribution_type and contribution_type != "ALL":
                search_text = f"{amount} {contribution_type}"
            elif contribution_type and contribution_type != "ALL":
                search_text = f"{contribution_type} contributions"
            else:
                search_text = "contributions"

            embed = discord.Embed(
                title="ℹ️ No Changes",
                description=f"No {search_text} found to remove for **{in_game_name}**",
                color=discord.Color.blue()
            )

        await interaction.followup.send(embed=embed)

        # Update the summary embed asynchronously (don't wait for it)
        bot.loop.create_task(update_summary_embed())

    except discord.errors.NotFound:
        # Interaction expired
        print(f"Interaction expired for requirementsremove command by {interaction.user.display_name}")
    except Exception as e:
        print(f"Error in requirementsremove command: {e}")
        try:
            if not interaction.response.is_done():
                embed = discord.Embed(
                    title="❌ Error",
                    description="An error occurred while processing the removal. Please try again.",
                    color=discord.Color.red()
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
            else:
                embed = discord.Embed(
                    title="❌ Error",
                    description="An error occurred while processing the removal. Please try again.",
                    color=discord.Color.red()
                )
                await interaction.followup.send(embed=embed, ephemeral=True)
        except:
            pass

@bot.tree.command(name="deleteuser", description="[ADMIN] Delete a user and all their data")
@app_commands.describe(in_game_name="The in-game name of the user to delete")
async def delete_user_command(interaction: discord.Interaction, in_game_name: str):
    """Delete a user and all their contributions."""
    try:
        # Check if command is used in allowed channel
        if not check_allowed_channel(interaction):
            await send_wrong_channel_message(interaction)
            return

        # Check if user has admin permissions
        if not (interaction.user.guild_permissions.administrator or is_admin(interaction.user.id)):
            embed = discord.Embed(
                title="❌ Access Denied",
                description="You need administrator permissions to use this command.",
                color=discord.Color.red()
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        # Check if user exists
        user_info = await database.get_user_by_ign(in_game_name)
        if not user_info:
            embed = discord.Embed(
                title="❌ User Not Found",
                description=f"No user found with in-game name: **{in_game_name}**",
                color=discord.Color.red()
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        # Defer for longer operations
        await interaction.response.defer()

        # Delete user
        changes = await database.delete_user(in_game_name)

        embed = discord.Embed(
            title="✅ User Deleted",
            description=f"Deleted user **{in_game_name}** and all their data ({changes} records deleted)",
            color=discord.Color.green()
        )

        await interaction.followup.send(embed=embed)

        # Update the summary embed asynchronously (don't wait for it)
        bot.loop.create_task(update_summary_embed())

    except discord.errors.NotFound:
        print(f"Interaction expired for deleteuser command by {interaction.user.display_name}")
    except Exception as e:
        print(f"Error in deleteuser command: {e}")
        try:
            if not interaction.response.is_done():
                embed = discord.Embed(
                    title="❌ Error",
                    description="An error occurred while deleting the user. Please try again.",
                    color=discord.Color.red()
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
            else:
                embed = discord.Embed(
                    title="❌ Error",
                    description="An error occurred while deleting the user. Please try again.",
                    color=discord.Color.red()
                )
                await interaction.followup.send(embed=embed, ephemeral=True)
        except:
            pass

@bot.tree.command(name="listusers", description="[ADMIN] List all registered users")
async def list_users_command(interaction: discord.Interaction):
    """List all registered users."""
    # Check if command is used in allowed channel
    if not check_allowed_channel(interaction):
        await send_wrong_channel_message(interaction)
        return

    # Check if user has admin permissions
    if not (interaction.user.guild_permissions.administrator or is_admin(interaction.user.id)):
        embed = discord.Embed(
            title="❌ Access Denied",
            description="You need administrator permissions to use this command.",
            color=discord.Color.red()
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return

    users = await database.get_all_users()

    if not users:
        embed = discord.Embed(
            title="📋 User List",
            description="No users registered yet.",
            color=discord.Color.blue()
        )
    else:
        embed = discord.Embed(
            title="📋 Registered Users",
            description=f"Total: {len(users)} users",
            color=discord.Color.blue()
        )

        user_list = []
        for discord_id, ign in users:
            user_list.append(f"• **{ign}** (<@{discord_id}>)")

        # Split into chunks if too many users
        chunk_size = 20
        for i in range(0, len(user_list), chunk_size):
            chunk = user_list[i:i+chunk_size]
            embed.add_field(
                name=f"Users {i+1}-{min(i+chunk_size, len(user_list))}",
                value="\n".join(chunk),
                inline=False
            )

    await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="resetleaderboard", description="[ADMIN] Reset the entire leaderboard (DANGER: Deletes ALL data)")
async def reset_leaderboard_command(interaction: discord.Interaction):
    """Reset the entire leaderboard by deleting all users and contributions."""
    # Check if command is used in allowed channel
    if not check_allowed_channel(interaction):
        await send_wrong_channel_message(interaction)
        return

    # Check if user has admin permissions
    if not (interaction.user.guild_permissions.administrator or is_admin(interaction.user.id)):
        embed = discord.Embed(
            title="❌ Access Denied",
            description="You need administrator permissions to use this command.",
            color=discord.Color.red()
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return

    # Create confirmation embed with warning
    embed = discord.Embed(
        title="⚠️ DANGER: Reset Leaderboard",
        description="**This will permanently delete ALL data:**\n\n"
                   "• All user accounts and sync data\n"
                   "• All contribution records\n"
                   "• All historical data\n\n"
                   "**This action CANNOT be undone!**\n\n"
                   "Are you absolutely sure you want to proceed?",
        color=discord.Color.red()
    )

    # Create confirmation view
    view = ResetConfirmationView()
    await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

@bot.tree.command(name="monthwipe", description="[ADMIN] Wipe all contributions but keep user accounts")
async def month_wipe_command(interaction: discord.Interaction):
    """Wipe all contributions but keep users synced."""
    # Check if command is used in allowed channel
    if not check_allowed_channel(interaction):
        await send_wrong_channel_message(interaction)
        return

    # Check if user has admin permissions
    if not (interaction.user.guild_permissions.administrator or is_admin(interaction.user.id)):
        embed = discord.Embed(
            title="❌ Access Denied",
            description="You need administrator permissions to use this command.",
            color=discord.Color.red()
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return

    # Get current stats before wiping
    users = await database.get_all_users()
    contributions = await database.get_all_contributions()

    # Create confirmation embed
    embed = discord.Embed(
        title="⚠️ Month Wipe Confirmation",
        description=f"**This will delete all contribution data:**\n\n"
                   f"• {len(contributions)} contribution records will be deleted\n"
                   f"• {len(users)} user accounts will be PRESERVED\n"
                   f"• Users will stay synced and can immediately add new contributions\n\n"
                   f"**Perfect for monthly resets!**\n\n"
                   f"Are you sure you want to proceed?",
        color=discord.Color.orange()
    )

    # Create confirmation view
    view = MonthWipeConfirmationView()
    await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

@bot.tree.command(name="pointvalue", description="[ADMIN] Set point values for contribution types")
@app_commands.describe(
    contribution_type="Type of contribution to set point value for",
    points_per_unit="Points awarded per unit (e.g., 0.000001 for 1 point per 1M EXP)"
)
@app_commands.choices(contribution_type=[
    app_commands.Choice(name="EXP", value="EXP"),
    app_commands.Choice(name="SHULKER OF SHARDS", value="SHULKEROFSHARDS"),
    app_commands.Choice(name="IGM", value="IGM")
])
async def point_value_command(interaction: discord.Interaction, contribution_type: str, points_per_unit: float):
    """Set the point value for a contribution type."""
    # Check if command is used in allowed channel
    if not check_allowed_channel(interaction):
        await send_wrong_channel_message(interaction)
        return

    # Check if user has admin permissions
    if not (interaction.user.guild_permissions.administrator or is_admin(interaction.user.id)):
        embed = discord.Embed(
            title="❌ Access Denied",
            description="You need administrator permissions to use this command.",
            color=discord.Color.red()
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return

    # Validate points_per_unit
    if points_per_unit < 0:
        embed = discord.Embed(
            title="❌ Invalid Value",
            description="Points per unit must be 0 or greater.",
            color=discord.Color.red()
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return

    # Update the point value
    changes = await database.update_point_value(contribution_type, points_per_unit)

    if changes > 0:
        # Format the display name
        display_name = "Shulker of Shards" if contribution_type == "SHULKEROFSHARDS" else contribution_type

        # Create helpful description based on the value
        if points_per_unit == 0:
            description_text = f"**{display_name}** contributions will **not count** for points."
        elif contribution_type == "EXP" and points_per_unit == 0.000001:
            description_text = f"**{display_name}** contributions will award **1 point per 1 million EXP**."
        elif contribution_type == "SHULKEROFSHARDS" and points_per_unit == 1.0:
            description_text = f"**{display_name}** contributions will award **1 point per shulker**."
        else:
            description_text = f"**{display_name}** contributions will award **{points_per_unit} points per unit**."

        embed = discord.Embed(
            title="✅ Point Value Updated",
            description=description_text,
            color=discord.Color.green()
        )

        # Show current point values
        point_values = await database.get_point_values()
        current_values = []
        for contrib_type, value in point_values.items():
            display_name = "Shulker of Shards" if contrib_type == "SHULKEROFSHARDS" else contrib_type
            if value == 0:
                current_values.append(f"• **{display_name}**: No points")
            elif contrib_type == "EXP" and value == 0.000001:
                current_values.append(f"• **{display_name}**: 1 point per 1M")
            elif contrib_type == "SHULKEROFSHARDS" and value == 1.0:
                current_values.append(f"• **{display_name}**: 1 point per shulker")
            else:
                current_values.append(f"• **{display_name}**: {value} points per unit")

        embed.add_field(
            name="📊 Current Point Values",
            value="\n".join(current_values),
            inline=False
        )

        await interaction.response.send_message(embed=embed)

        # Update the leaderboard to reflect new point values asynchronously
        bot.loop.create_task(update_summary_embed())
    else:
        embed = discord.Embed(
            title="❌ Update Failed",
            description="Failed to update point value. Please try again.",
            color=discord.Color.red()
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="fullleaderboard", description="[ADMIN] View complete leaderboard with all players and points")
async def full_leaderboard_command(interaction: discord.Interaction):
    """Show the complete leaderboard with all players and their points (admin only)."""
    # Check if command is used in allowed channel
    if not check_allowed_channel(interaction):
        await send_wrong_channel_message(interaction)
        return

    # Check if user has admin permissions
    if not (interaction.user.guild_permissions.administrator or is_admin(interaction.user.id)):
        embed = discord.Embed(
            title="❌ Access Denied",
            description="You need administrator permissions to use this command.",
            color=discord.Color.red()
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return

    # Get all contributions
    contributions = await database.get_all_contributions()

    # Group contributions by user
    user_contributions = {}
    for ign, contrib_type, amount in contributions:
        if ign not in user_contributions:
            user_contributions[ign] = {"IGM": 0, "SHULKEROFSHARDS": 0, "EXP": 0}

        try:
            numeric_amount = parse_amount(amount)
            user_contributions[ign][contrib_type] += numeric_amount
        except ValueError:
            print(f"Warning: Could not parse amount '{amount}' for {ign} {contrib_type}")
            pass

    if not user_contributions:
        embed = discord.Embed(
            title="📊 Complete Leaderboard",
            description="No contributions recorded yet.",
            color=discord.Color.orange()
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)
        return

    # Calculate points for all users and sort
    user_points = {}
    for ign, contribs in user_contributions.items():
        points = await calculate_user_points(contribs)
        user_points[ign] = points

    # Sort users by total points
    sorted_users = sorted(user_contributions.items(),
                         key=lambda x: user_points[x[0]],
                         reverse=True)

    # Create embed
    embed = discord.Embed(
        title="📊 Complete Leaderboard - All Players",
        color=discord.Color.blue()
    )

    # Calculate totals
    total_igm = sum(user['IGM'] for user in user_contributions.values())
    total_exp = sum(user['EXP'] for user in user_contributions.values())
    total_shards = sum(user['SHULKEROFSHARDS'] for user in user_contributions.values())
    total_points = sum(user_points.values())

    embed.description = f"🏆 **{total_points:.1f} Total Points** • {len(user_contributions)} Players\n\n💰 Total IGM: {format_number(total_igm)}\n🪙 Total Shulkers of Shards: {format_number(total_shards)}\n⭐ Total EXP: {format_number(total_exp)}"

    # Add all users in a compact format
    leaderboard_text = ""
    for i, (ign, contribs) in enumerate(sorted_users):
        rank = i + 1
        points = user_points[ign]

        # Get rank emoji
        if rank == 1:
            rank_emoji = "🥇"
        elif rank == 2:
            rank_emoji = "🥈"
        elif rank == 3:
            rank_emoji = "🥉"
        elif rank <= 10:
            rank_emoji = "🏅"
        else:
            rank_emoji = "📍"

        # Format contributions compactly
        igm_str = format_number(contribs['IGM']) if contribs['IGM'] > 0 else "0"
        exp_str = format_number(contribs['EXP']) if contribs['EXP'] > 0 else "0"
        shards_str = format_number(contribs['SHULKEROFSHARDS']) if contribs['SHULKEROFSHARDS'] > 0 else "0"

        leaderboard_text += f"{rank_emoji} **#{rank} {ign}** - {points:.1f} pts\n"
        leaderboard_text += f"   💰 {igm_str} • 🪙 {shards_str} • ⭐ {exp_str}\n\n"

        # Split into multiple fields if too long (Discord limit ~1024 chars per field)
        if len(leaderboard_text) > 900:
            embed.add_field(
                name=f"Rankings {len(embed.fields) * 10 + 1}-{rank}",
                value=leaderboard_text,
                inline=False
            )
            leaderboard_text = ""

    # Add remaining text
    if leaderboard_text:
        start_rank = len(embed.fields) * 10 + 1
        embed.add_field(
            name=f"Rankings {start_rank}-{len(sorted_users)}",
            value=leaderboard_text,
            inline=False
        )

    # Add timestamp
    embed.timestamp = discord.utils.utcnow()
    embed.set_footer(text="Admin-only view • Made by Cerv <3")

    await interaction.response.send_message(embed=embed, ephemeral=True)


class ResetConfirmationView(discord.ui.View):
    """Confirmation view for resetting the leaderboard."""

    def __init__(self):
        super().__init__(timeout=30)  # 30 second timeout for safety

    @discord.ui.button(label='❌ Cancel', style=discord.ButtonStyle.secondary)
    async def cancel_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        embed = discord.Embed(
            title="✅ Reset Cancelled",
            description="The leaderboard reset has been cancelled. No data was deleted.",
            color=discord.Color.green()
        )
        await interaction.response.edit_message(embed=embed, view=None)

    @discord.ui.button(label='🗑️ CONFIRM RESET', style=discord.ButtonStyle.danger)
    async def confirm_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        # Double-check admin permissions
        if not (interaction.user.guild_permissions.administrator or is_admin(interaction.user.id)):
            embed = discord.Embed(
                title="❌ Access Denied",
                description="You need administrator permissions to perform this action.",
                color=discord.Color.red()
            )
            await interaction.response.edit_message(embed=embed, view=None)
            return

        # Perform the reset
        try:
            users_deleted, contributions_deleted = await database.reset_all_data()

            embed = discord.Embed(
                title="✅ Leaderboard Reset Complete",
                description=f"**All data has been permanently deleted:**\n\n"
                           f"• {users_deleted} users removed\n"
                           f"• {contributions_deleted} contribution records deleted\n\n"
                           f"The leaderboard is now completely empty.",
                color=discord.Color.green()
            )

            await interaction.response.edit_message(embed=embed, view=None)

            # Update the summary embed to show empty state
            await update_summary_embed()

        except Exception as e:
            embed = discord.Embed(
                title="❌ Reset Failed",
                description=f"An error occurred while resetting the leaderboard:\n```{str(e)}```",
                color=discord.Color.red()
            )
            await interaction.response.edit_message(embed=embed, view=None)

    async def on_timeout(self):
        """Handle timeout - disable all buttons."""
        for item in self.children:
            item.disabled = True

class MonthWipeConfirmationView(discord.ui.View):
    """Confirmation view for wiping contributions (month reset)."""

    def __init__(self):
        super().__init__(timeout=30)  # 30 second timeout for safety

    @discord.ui.button(label='❌ Cancel', style=discord.ButtonStyle.secondary)
    async def cancel_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        embed = discord.Embed(
            title="✅ Month Wipe Cancelled",
            description="The month wipe has been cancelled. No data was deleted.",
            color=discord.Color.green()
        )
        await interaction.response.edit_message(embed=embed, view=None)

    @discord.ui.button(label='🧹 CONFIRM WIPE', style=discord.ButtonStyle.danger)
    async def confirm_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        # Double-check admin permissions
        if not (interaction.user.guild_permissions.administrator or is_admin(interaction.user.id)):
            embed = discord.Embed(
                title="❌ Access Denied",
                description="You need administrator permissions to perform this action.",
                color=discord.Color.red()
            )
            await interaction.response.edit_message(embed=embed, view=None)
            return

        # Perform the wipe
        try:
            contributions_deleted = await database.wipe_contributions()

            embed = discord.Embed(
                title="✅ Month Wipe Complete",
                description=f"**All contribution data has been wiped:**\n\n"
                           f"• {contributions_deleted} contribution records deleted\n"
                           f"• All user accounts preserved\n"
                           f"• Users remain synced and ready for new contributions\n\n"
                           f"**Fresh start for the new month!** 🎉",
                color=discord.Color.green()
            )

            await interaction.response.edit_message(embed=embed, view=None)

            # Update the summary embed to show empty state
            await update_summary_embed()

        except Exception as e:
            embed = discord.Embed(
                title="❌ Wipe Failed",
                description=f"An error occurred while wiping contributions:\n```{str(e)}```",
                color=discord.Color.red()
            )
            await interaction.response.edit_message(embed=embed, view=None)

    async def on_timeout(self):
        """Handle timeout - disable all buttons."""
        for item in self.children:
            item.disabled = True

class LeaderboardView(discord.ui.View):
    """Enhanced leaderboard view with time period filtering."""

    def __init__(self, all_contributions):
        super().__init__(timeout=None)  # No timeout to prevent interaction failures
        self.all_contributions = all_contributions
        self.current_period = "this_month"  # Default to this month
        self.embed = None

        # Generate initial embed (will be set properly when first used)
        # We can't call async function from __init__, so embed will be None initially

    async def update_embed(self):
        """Update embed based on current time period."""
        # Filter contributions based on current period
        if self.current_period == "week1":
            start_date, end_date = get_week_range(1)
            period_name = "Week 1"
        elif self.current_period == "week2":
            start_date, end_date = get_week_range(2)
            period_name = "Week 2"
        elif self.current_period == "week3":
            start_date, end_date = get_week_range(3)
            period_name = "Week 3"
        elif self.current_period == "week4":
            start_date, end_date = get_week_range(4)
            period_name = "Week 4"
        else:  # this_month
            start_date, end_date = get_month_range()
            period_name = "This Month"

        # Filter contributions
        filtered_contributions = filter_contributions_by_date(self.all_contributions, start_date, end_date)

        # Group contributions by user
        user_contributions = {}
        for ign, contrib_type, amount in filtered_contributions:
            if ign not in user_contributions:
                user_contributions[ign] = {"IGM": 0, "SHULKEROFSHARDS": 0, "EXP": 0}

            try:
                numeric_amount = parse_amount(amount)
                user_contributions[ign][contrib_type] += numeric_amount
            except ValueError:
                print(f"Warning: Could not parse amount '{amount}' for {ign} {contrib_type}")
                pass

        # Create single embed with all users
        self.embed = await create_single_summary_embed(user_contributions, period_name)

        # Update button states
        self.update_buttons()

    def update_buttons(self):
        """Update button states based on current period."""
        # Update time period button styles
        self.week1_button.style = discord.ButtonStyle.primary if self.current_period == "week1" else discord.ButtonStyle.secondary
        self.week2_button.style = discord.ButtonStyle.primary if self.current_period == "week2" else discord.ButtonStyle.secondary
        self.week3_button.style = discord.ButtonStyle.primary if self.current_period == "week3" else discord.ButtonStyle.secondary
        self.week4_button.style = discord.ButtonStyle.primary if self.current_period == "week4" else discord.ButtonStyle.secondary
        self.month_button.style = discord.ButtonStyle.primary if self.current_period == "this_month" else discord.ButtonStyle.secondary

    # Time period buttons
    @discord.ui.button(label='Week 1', style=discord.ButtonStyle.secondary, row=0)
    async def week1_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.current_period = "week1"
        await self.update_embed()
        await interaction.response.edit_message(embed=self.embed, view=self)

    @discord.ui.button(label='Week 2', style=discord.ButtonStyle.secondary, row=0)
    async def week2_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.current_period = "week2"
        await self.update_embed()
        await interaction.response.edit_message(embed=self.embed, view=self)

    @discord.ui.button(label='Week 3', style=discord.ButtonStyle.secondary, row=0)
    async def week3_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.current_period = "week3"
        await self.update_embed()
        await interaction.response.edit_message(embed=self.embed, view=self)

    @discord.ui.button(label='Week 4', style=discord.ButtonStyle.secondary, row=0)
    async def week4_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.current_period = "week4"
        await self.update_embed()
        await interaction.response.edit_message(embed=self.embed, view=self)

    @discord.ui.button(label='This Month', style=discord.ButtonStyle.primary, row=0)
    async def month_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.current_period = "this_month"
        await self.update_embed()
        await interaction.response.edit_message(embed=self.embed, view=self)

async def create_single_summary_embed(user_contributions, period_name="This Month"):
    """Create a single embed for the contribution summary without pagination."""

    if not user_contributions:
        embed = discord.Embed(
            title=f"🏆 Revenants Leaderboard - {period_name}",
            description="📋 No contributions recorded for this period\n\n🔗 Use /sync to link your account\n💎 Use /requirements to add contributions",
            color=0x2F3136  # Dark theme color
        )
        return embed

    # Calculate points for all users and sort
    user_points = {}
    for ign, contribs in user_contributions.items():
        points = await calculate_user_points(contribs)
        user_points[ign] = points

    # Sort users by total points
    sorted_users = sorted(user_contributions.items(),
                         key=lambda x: user_points[x[0]],
                         reverse=True)

    # Calculate grand totals
    total_igm = sum(user['IGM'] for user in user_contributions.values())
    total_exp = sum(user['EXP'] for user in user_contributions.values())
    total_shards = sum(user['SHULKEROFSHARDS'] for user in user_contributions.values())

    # Create embed with dynamic color
    embed = discord.Embed(
        title=f"🏆 Revenants Leaderboard - {period_name}",
        color=0x5865F2
    )

    # Add description with stats
    embed.description = f"💰 Total IGM: {format_number(total_igm)}\n🪙 Total Shulkers of Shards: {format_number(total_shards)}\n⭐ Total EXP: {format_number(total_exp)}"

    # Add users (limit to top 5 for better performance and responsiveness)
    max_users = min(5, len(sorted_users))
    for i, (ign, contribs) in enumerate(sorted_users[:max_users]):
        # Calculate rank
        rank = i + 1

        # Get rank emoji
        if rank == 1:
            rank_emoji = "🥇"
        elif rank == 2:
            rank_emoji = "🥈"
        elif rank == 3:
            rank_emoji = "🥉"
        elif rank <= 10:
            rank_emoji = "🏅"
        else:
            rank_emoji = "📍"

        # Format numbers
        igm_val = contribs['IGM']
        exp_val = contribs['EXP']
        shards_val = contribs['SHULKEROFSHARDS']

        # Get user's points from pre-calculated values
        user_point_total = user_points[ign]

        # Create progress bars for visual appeal
        def create_progress_bar(value, max_value, length=8):
            if max_value == 0:
                return "▱" * length
            filled = int((value / max_value) * length)
            return "▰" * filled + "▱" * (length - filled)

        # Calculate max values for progress bars
        max_igm = max(user['IGM'] for user in user_contributions.values()) if user_contributions else 1
        max_exp = max(user['EXP'] for user in user_contributions.values()) if user_contributions else 1
        max_shards = max(user['SHULKEROFSHARDS'] for user in user_contributions.values()) if user_contributions else 1

        # Format displays with progress bars
        igm_display = format_number(igm_val) if igm_val > 0 else "0"
        exp_display = format_number(exp_val) if exp_val > 0 else "0"
        shards_display = format_number(shards_val) if shards_val > 0 else "0"

        igm_bar = create_progress_bar(igm_val, max_igm)
        exp_bar = create_progress_bar(exp_val, max_exp)
        shards_bar = create_progress_bar(shards_val, max_shards)

        # Create field value with better formatting including points
        field_value = f"🏆 **{user_point_total:.1f} Points**\n💰 IGM: {igm_display} {igm_bar}\n🪙 Shulkers of Shards: {shards_display} {shards_bar}\n⭐ EXP: {exp_display} {exp_bar}"

        embed.add_field(
            name=f"{rank_emoji} #{rank} {ign}",
            value=field_value,
            inline=True
        )

    # Add empty fields to balance layout if needed
    while len(embed.fields) % 3 != 0:
        embed.add_field(name="\u200b", value="\u200b", inline=True)

    # Add timestamp
    embed.timestamp = discord.utils.utcnow()
    embed.set_footer(text="Made by Cerv <3")

    return embed

def create_summary_embeds(user_contributions, period_name="This Month"):
    """Create paginated embeds for the contribution summary."""
    embeds = []

    if not user_contributions:
        embed = discord.Embed(
            title=f"🏆 Revenants Leaderboard - {period_name}",
            description="📋 No contributions recorded for this period\n\n🔗 Use /sync to link your account\n💎 Use /requirements to add contributions",
            color=0x2F3136  # Dark theme color
        )
        return [embed]

    # Calculate total points for sorting using database point values
    def calculate_total_value(contribs):
        # Use hardcoded values for now, will be updated to use database values
        # Point system: 1 Shulker = 1 point, 1M EXP = 1 point, IGM = 0 points
        exp_points = contribs['EXP'] / 1000000  # Convert EXP to millions for points
        shulker_points = contribs['SHULKEROFSHARDS']  # 1 shulker = 1 point
        # IGM doesn't count for points
        return exp_points + shulker_points

    # Sort users by total contribution value
    sorted_users = sorted(user_contributions.items(),
                         key=lambda x: calculate_total_value(x[1]),
                         reverse=True)

    # Split users into pages (9 users per page, 3 columns of 3)
    users_per_page = 9
    total_pages = (len(sorted_users) + users_per_page - 1) // users_per_page

    # Calculate grand totals
    total_igm = sum(user['IGM'] for user in user_contributions.values())
    total_exp = sum(user['EXP'] for user in user_contributions.values())
    total_shards = sum(user['SHULKEROFSHARDS'] for user in user_contributions.values())
    total_contributors = len(user_contributions)

    for page_num in range(total_pages):
        start_idx = page_num * users_per_page
        end_idx = min(start_idx + users_per_page, len(sorted_users))
        page_users = sorted_users[start_idx:end_idx]

        # Create embed with gradient color based on page
        colors = [0x5865F2, 0x57F287, 0xFEE75C, 0xED4245, 0xEB459E]
        embed_color = colors[page_num % len(colors)]

        embed = discord.Embed(
            title=f"🏆 Revenants Leaderboard - {period_name}",
            color=embed_color
        )

        # Add description with stats
        if total_pages > 1:
            embed.description = f"📊 Page {page_num + 1} of {total_pages}\n\n💰 Total IGM: {format_number(total_igm)}\n🪙 Total Shulkers of Shards: {format_number(total_shards)}\n⭐ Total EXP: {format_number(total_exp)}"
        else:
            embed.description = f"💰 Total IGM: {format_number(total_igm)}\n🪙 Total Shulkers of Shards: {format_number(total_shards)}\n⭐ Total EXP: {format_number(total_exp)}"

        # Add users in a more organized way
        for i, (ign, contribs) in enumerate(page_users):
            # Calculate rank
            rank = start_idx + i + 1

            # Get rank emoji
            if rank == 1:
                rank_emoji = "🥇"
            elif rank == 2:
                rank_emoji = "🥈"
            elif rank == 3:
                rank_emoji = "🥉"
            elif rank <= 10:
                rank_emoji = "🏅"
            else:
                rank_emoji = "📍"

            # Format numbers
            igm_val = contribs['IGM']
            exp_val = contribs['EXP']
            shards_val = contribs['SHULKEROFSHARDS']

            # Create progress bars for visual appeal
            def create_progress_bar(value, max_value, length=8):
                if max_value == 0:
                    return "▱" * length
                filled = int((value / max_value) * length)
                return "▰" * filled + "▱" * (length - filled)

            # Calculate max values for progress bars
            max_igm = max(user['IGM'] for user in user_contributions.values()) if user_contributions else 1
            max_exp = max(user['EXP'] for user in user_contributions.values()) if user_contributions else 1
            max_shards = max(user['SHULKEROFSHARDS'] for user in user_contributions.values()) if user_contributions else 1

            # Format displays with progress bars
            igm_display = format_number(igm_val) if igm_val > 0 else "0"
            exp_display = format_number(exp_val) if exp_val > 0 else "0"
            shards_display = format_number(shards_val) if shards_val > 0 else "0"

            igm_bar = create_progress_bar(igm_val, max_igm)
            exp_bar = create_progress_bar(exp_val, max_exp)
            shards_bar = create_progress_bar(shards_val, max_shards)

            # Create field value with better formatting
            field_value = f"💰 IGM: {igm_display} {igm_bar}\n🪙 Shulkers of Shards: {shards_display} {shards_bar}\n⭐ EXP: {exp_display} {exp_bar}"

            embed.add_field(
                name=f"{rank_emoji} #{rank} {ign}",
                value=field_value,
                inline=True
            )

        # Add empty fields to balance layout if needed
        while len(embed.fields) % 3 != 0:
            embed.add_field(name="\u200b", value="\u200b", inline=True)

        # Add timestamp
        embed.timestamp = discord.utils.utcnow()
        embed.set_footer(text="Made by Cerv <3")

        embeds.append(embed)

    return embeds

async def update_summary_embed():
    """Update or create the summary embed in the specified channel."""
    channel = bot.get_channel(CHANNEL_ID)
    if not channel:
        print(f"Could not find channel with ID {CHANNEL_ID}")
        return

    # Get all contributions with timestamps
    contributions = await database.get_all_contributions()

    # Try to find existing message and update it
    try:
        messages = [message async for message in channel.history(limit=50)]
        # Look specifically for leaderboard messages (messages with embeds that have "Revenants Leaderboard" in title)
        # Skip the old message ID that should be ignored
        OLD_MESSAGE_ID = 1377547214657945652
        bot_messages = []
        for msg in messages:
            if (msg.author == bot.user and msg.embeds and
                len(msg.embeds) > 0 and msg.embeds[0].title and
                "Revenants Leaderboard" in msg.embeds[0].title and
                msg.id != OLD_MESSAGE_ID):  # Skip the old message
                bot_messages.append(msg)
                break  # Only need the most recent leaderboard message

        # Create new leaderboard view with time period filtering
        view = LeaderboardView(contributions)
        await view.update_embed()  # Initialize the embed

        if bot_messages:
            # Update existing message
            print(f"Updating existing leaderboard message (ID: {bot_messages[0].id})")
            await bot_messages[0].edit(embed=view.embed, view=view)
        else:
            # Send new message only if no leaderboard found
            print("No existing leaderboard found, creating new one")
            await channel.send(embed=view.embed, view=view)

    except Exception as e:
        print(f"Error updating embed: {e}")
        import traceback
        traceback.print_exc()
        # Fallback: send new embed
        try:
            view = LeaderboardView(contributions)
            await view.update_embed()  # Initialize the embed
            await channel.send(embed=view.embed, view=view)
        except Exception as e2:
            print(f"Error sending new embed: {e2}")

# Run the bot
if __name__ == "__main__":
    bot.run(os.getenv('DISCORD_TOKEN'))
