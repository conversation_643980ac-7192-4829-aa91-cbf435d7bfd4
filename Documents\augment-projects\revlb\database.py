import aiosqlite
import os

DATABASE_PATH = "contributions.db"

def _parse_amount_simple(amount_str):
    """Simple amount parser to avoid circular imports."""
    # Remove whitespace and convert to lowercase
    amount_str = amount_str.strip().lower()

    # Remove commas
    amount_str = amount_str.replace(',', '')

    # Handle different suffixes - convert everything to base units
    if amount_str.endswith('b'):
        # Billions
        amount_str = amount_str[:-1]
        base_value = float(amount_str)
        return base_value * 1000000000  # Convert to base units
    elif amount_str.endswith('m'):
        # Millions
        amount_str = amount_str[:-1]
        base_value = float(amount_str)
        return base_value * 1000000  # Convert to base units
    elif amount_str.endswith('k'):
        # Thousands
        amount_str = amount_str[:-1]
        base_value = float(amount_str)
        return base_value * 1000  # Convert to base units
    else:
        # No suffix - treat as base units
        base_value = float(amount_str)
        return base_value  # Keep as base units

async def init_database():
    """Initialize the database with required tables."""
    async with aiosqlite.connect(DATABASE_PATH) as db:
        # Users table to link Discord ID to in-game name
        await db.execute("""
            CREATE TABLE IF NOT EXISTS users (
                discord_id INTEGER PRIMARY KEY,
                in_game_name TEXT NOT NULL UNIQUE
            )
        """)

        # Contributions table
        await db.execute("""
            CREATE TABLE IF NOT EXISTS contributions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                discord_id INTEGER,
                contribution_type TEXT NOT NULL,
                amount TEXT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (discord_id) REFERENCES users (discord_id)
            )
        """)

        # Create point_values table for customizable point system
        await db.execute("""
            CREATE TABLE IF NOT EXISTS point_values (
                contribution_type TEXT PRIMARY KEY,
                points_per_unit REAL NOT NULL
            )
        """)

        # Insert default point values if they don't exist
        await db.execute("""
            INSERT OR IGNORE INTO point_values (contribution_type, points_per_unit) VALUES
            ('EXP', 0.000001),
            ('SHULKEROFSHARDS', 1.0),
            ('IGM', 0.0)
        """)

        await db.commit()

async def sync_user(discord_id: int, in_game_name: str):
    """Sync a Discord user with their in-game name."""
    async with aiosqlite.connect(DATABASE_PATH) as db:
        try:
            await db.execute(
                "INSERT OR REPLACE INTO users (discord_id, in_game_name) VALUES (?, ?)",
                (discord_id, in_game_name)
            )
            await db.commit()
            return True
        except Exception as e:
            print(f"Error syncing user: {e}")
            return False

async def is_user_synced(discord_id: int):
    """Check if a user is synced."""
    async with aiosqlite.connect(DATABASE_PATH) as db:
        cursor = await db.execute(
            "SELECT in_game_name FROM users WHERE discord_id = ?",
            (discord_id,)
        )
        result = await cursor.fetchone()
        return result is not None

async def get_user_ign(discord_id: int):
    """Get user's in-game name."""
    async with aiosqlite.connect(DATABASE_PATH) as db:
        cursor = await db.execute(
            "SELECT in_game_name FROM users WHERE discord_id = ?",
            (discord_id,)
        )
        result = await cursor.fetchone()
        return result[0] if result else None

async def add_contribution(discord_id: int, contribution_type: str, amount: str):
    """Add a contribution for a user."""
    async with aiosqlite.connect(DATABASE_PATH) as db:
        await db.execute(
            "INSERT INTO contributions (discord_id, contribution_type, amount) VALUES (?, ?, ?)",
            (discord_id, contribution_type, amount)
        )
        await db.commit()

async def get_all_contributions():
    """Get all contributions with timestamps."""
    async with aiosqlite.connect(DATABASE_PATH) as db:
        cursor = await db.execute("""
            SELECT u.in_game_name, c.contribution_type, c.amount, c.timestamp
            FROM users u
            JOIN contributions c ON u.discord_id = c.discord_id
            ORDER BY c.timestamp DESC
        """)
        return await cursor.fetchall()

async def remove_user_contributions(in_game_name: str, contribution_type: str = None, amount: str = None):
    """Remove contributions for a user by in-game name."""
    async with aiosqlite.connect(DATABASE_PATH) as db:
        if contribution_type and amount:
            # Remove specific contribution type with specific amount (only one entry)
            # First find the ID of one matching record
            cursor = await db.execute("""
                SELECT id FROM contributions
                WHERE discord_id = (SELECT discord_id FROM users WHERE in_game_name = ?)
                AND contribution_type = ? AND amount = ?
                LIMIT 1
            """, (in_game_name, contribution_type, amount))
            result = await cursor.fetchone()

            if result:
                # Delete the specific record by ID
                await db.execute("DELETE FROM contributions WHERE id = ?", (result[0],))
            changes = 1 if result else 0
        elif contribution_type:
            # Remove all contributions of specific type
            await db.execute("""
                DELETE FROM contributions
                WHERE discord_id = (SELECT discord_id FROM users WHERE in_game_name = ?)
                AND contribution_type = ?
            """, (in_game_name, contribution_type))
            changes = db.total_changes
        else:
            # Remove all contributions for user
            await db.execute("""
                DELETE FROM contributions
                WHERE discord_id = (SELECT discord_id FROM users WHERE in_game_name = ?)
            """, (in_game_name,))
            changes = db.total_changes

        await db.commit()
        return changes

async def get_user_by_ign(in_game_name: str):
    """Get user info by in-game name."""
    async with aiosqlite.connect(DATABASE_PATH) as db:
        cursor = await db.execute(
            "SELECT discord_id, in_game_name FROM users WHERE in_game_name = ?",
            (in_game_name,)
        )
        return await cursor.fetchone()

async def delete_user(in_game_name: str):
    """Delete a user and all their contributions."""
    async with aiosqlite.connect(DATABASE_PATH) as db:
        # First delete contributions
        await db.execute("""
            DELETE FROM contributions
            WHERE discord_id = (SELECT discord_id FROM users WHERE in_game_name = ?)
        """, (in_game_name,))

        # Then delete user
        await db.execute("DELETE FROM users WHERE in_game_name = ?", (in_game_name,))
        await db.commit()
        return db.total_changes

async def get_all_users():
    """Get all users."""
    async with aiosqlite.connect(DATABASE_PATH) as db:
        cursor = await db.execute("SELECT discord_id, in_game_name FROM users ORDER BY in_game_name")
        return await cursor.fetchall()

async def get_user_contributions(discord_id: int):
    """Get current contribution totals for a specific user."""
    async with aiosqlite.connect(DATABASE_PATH) as db:
        cursor = await db.execute("""
            SELECT contribution_type, amount
            FROM contributions
            WHERE discord_id = ?
        """, (discord_id,))

        contributions = await cursor.fetchall()

        # Group and sum contributions by type
        totals = {"IGM": 0, "SHULKEROFSHARDS": 0, "EXP": 0}

        for contrib_type, amount in contributions:
            try:
                # Parse amount directly here to avoid circular imports
                numeric_amount = _parse_amount_simple(amount)
                totals[contrib_type] += numeric_amount
            except ValueError:
                # If parsing fails, skip this contribution
                pass

        return totals

async def wipe_contributions():
    """Wipe all contributions but keep users synced."""
    async with aiosqlite.connect(DATABASE_PATH) as db:
        await db.execute("DELETE FROM contributions")
        contributions_deleted = db.total_changes
        await db.commit()
        return contributions_deleted

async def get_point_values():
    """Get current point values for all contribution types."""
    async with aiosqlite.connect(DATABASE_PATH) as db:
        cursor = await db.execute("SELECT contribution_type, points_per_unit FROM point_values")
        rows = await cursor.fetchall()
        return {row[0]: row[1] for row in rows}

async def update_point_value(contribution_type: str, points_per_unit: float):
    """Update the point value for a specific contribution type."""
    async with aiosqlite.connect(DATABASE_PATH) as db:
        await db.execute(
            "UPDATE point_values SET points_per_unit = ? WHERE contribution_type = ?",
            (points_per_unit, contribution_type)
        )
        await db.commit()
        return db.total_changes

async def reset_all_data():
    """Reset the entire leaderboard by deleting all users and contributions."""
    async with aiosqlite.connect(DATABASE_PATH) as db:
        # Delete all contributions first (due to foreign key constraints)
        await db.execute("DELETE FROM contributions")
        contributions_deleted = db.total_changes

        # Delete all users
        await db.execute("DELETE FROM users")
        users_deleted = db.total_changes

        await db.commit()
        return users_deleted, contributions_deleted
